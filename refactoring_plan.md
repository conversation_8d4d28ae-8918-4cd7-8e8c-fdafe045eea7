# Refactoring Plan: HomeScreen

This document outlines the detailed plan for refactoring the `HomeScreen` by separating concerns into a feature-first architecture.

## 1. File Mapping

The following table maps the original files to their new locations within the `lib/app/features/home/<USER>

| Original File Path                                                    | New File Path                                                     | Purpose                                          |
| :-------------------------------------------------------------------- | :---------------------------------------------------------------- | :----------------------------------------------- |
| `lib/views/home_screen/home_screen.dart`                              | `lib/app/features/home/<USER>/home_screen.dart`                     | Refactored `HomeScreen` UI (StatelessWidget)     |
| `lib/views/home_screen/home_screen.dart`                              | `lib/app/features/home/<USER>/home_provider.dart`              | State management for the Home screen             |
| `lib/views/home_screen/home_screen.dart`                              | `lib/app/features/home/<USER>/services/home_service.dart`           | Handles API calls for home screen data           |
| `lib/models/home_model/home_screen1_model/new_club_opening_model.dart`  | `lib/app/features/home/<USER>/models/new_club_opening_model.dart`   | Data model for club openings                     |
| `lib/models/home_model/home_screen1_model/fellow_reader_model.dart`     | `lib/app/features/home/<USER>/models/fellow_reader_model.dart`      | Data model for fellow readers                    |
| `lib/views/home_screen/home_screen.dart` (widget part)                | `lib/app/features/home/<USER>/widgets/club_openings_list.dart`      | Widget for the horizontal list of club openings  |
| `lib/views/home_screen/home_screen.dart` (widget part)                | `lib/app/features/home/<USER>/widgets/fellow_readers_list.dart`     | Widget for the horizontal list of fellow readers |
| `lib/views/home_screen/home_screen.dart` (widget part)                | `lib/app/features/home/<USER>/widgets/club_opening_card.dart`       | Widget for a single club opening item            |
| `lib/views/home_screen/home_screen.dart` (widget part)                | `lib/app/features/home/<USER>/widgets/fellow_reader_card.dart`      | Widget for a single fellow reader item           |

## 2. Refactoring Steps & Code Changes

### `home_service.dart`

This service will abstract the data fetching logic from the UI and controllers.

**Path:** `lib/app/features/home/<USER>/services/home_service.dart`

**Implementation Steps:**

1.  Create a `HomeService` class that depends on an `AppHttpClient`.
2.  Create a `getNewClubOpenings` method that encapsulates the API call currently in `BookClubController.getNewClubs`.
    *   **Endpoint:** (Determined by `BookClubController`'s implementation, e.g., `/api/clubs/new-openings`)
    *   **Payload:** `{ "userId": int, "offset": int, "limit": int }`
3.  Create a `getFellowReaders` method that encapsulates the API call currently in `UserController.getFellowReadersByUserId`.
    *   **Endpoint:** (Determined by `UserController`'s implementation, e.g., `/api/users/fellow-readers`)
    *   **Payload:** `{ "userId": int, "offset": int, "limit": int }`

### `home_provider.dart`

This provider will manage the state for the `HomeScreen`, using the `HomeService` to fetch data.

**Path:** `lib/app/features/home/<USER>/home_provider.dart`

**Implementation Steps:**

1.  Create a `HomeProvider` class with `ChangeNotifier`.
2.  Define state properties:
    *   `bool isLoading`: For the initial page load.
    *   `bool isLoadingMoreClubs`, `bool isLoadingMoreReaders`: For pagination.
    *   `List<NewClubOpeningList> clubList`: To hold club data.
    *   `List<FellowReader> fellowReadersList`: To hold reader data.
    *   Pagination properties: `clubOffset`, `readerOffset`, `clubCount`, `readerCount`.
3.  Create methods to manage state:
    *   `fetchInitialData()`: Calls both `_fetchNewClubOpenings` and `_fetchFellowReaders` in parallel and manages the main `isLoading` state.
    *   `loadMoreClubs()`: Fetches the next page of clubs, updates `clubList`, and manages `isLoadingMoreClubs`.
    *   `loadMoreReaders()`: Fetches the next page of readers, updates `fellowReadersList`, and manages `isLoadingMoreReaders`.
    *   `refreshClubs()` and `refreshReaders()`: Resets the respective lists and offsets and fetches the first page of data.

### `home_screen.dart` (Refactored)

The new `HomeScreen` will be a `StatelessWidget` that consumes `HomeProvider` for its data and state.

**Path:** `lib/app/features/home/<USER>/home_screen.dart`

**Implementation Steps:**

1.  Convert `HomeScreen` to a `StatelessWidget`.
2.  In the `build` method, use a `Consumer<HomeProvider>` to get access to the provider's state.
3.  Wrap the main content in a `Skeletonizer` widget, with its `enabled` property bound to `provider.isLoading`.
4.  Replace the main `SingleChildScrollView` with a `RefreshIndicator` that calls `provider.fetchInitialData()` on refresh.
5.  Replace the UI logic for the horizontal lists with new, dedicated widgets: `ClubOpeningsList` and `FellowReadersList`.

### Model & Widget Files

*   **Models (`lib/app/features/home/<USER>/models/`)**: Move `new_club_opening_model.dart` and `fellow_reader_model.dart` to this directory. No changes to the file contents are needed.
*   **Widgets (`lib/app/features/home/<USER>/widgets/`)**:
    *   **`club_openings_list.dart`**: Create a `StatefulWidget` that contains the horizontal `ListView`/`SingleChildScrollView`. It will have its own `ScrollController` to detect the end of the list and call `context.read<HomeProvider>().loadMoreClubs()`.
    *   **`fellow_readers_list.dart`**: Similar to `ClubOpeningsList`, but for fellow readers.
    *   **`club_opening_card.dart`**: A `StatelessWidget` that takes a `NewClubOpeningList` object and renders the card UI. This will contain the logic from the original `newClubOpeningSkeleton` builder.
    *   **`fellow_reader_card.dart`**: A `StatelessWidget` that takes a `FellowReader` object and renders its card UI, containing the logic from the original `fellowReaderSkeleton` builder.

## 3. Core Service Consolidation Plan

The following logic is not directly related to the `HomeScreen`'s UI and should be moved out to be handled globally or by dedicated services.

### `NotificationService` & `DeepLinkService`

The logic for handling notifications and deep links should be managed centrally.

*   **Code to be Removed from `_HomeScreenState`:**
    *   `WidgetsBinding.instance.addObserver(this);`
    *   `addPostFrameCallback((_) => checkDeepLink())`
    *   `FirebaseMessaging.onMessageOpenedApp.listen(...)`
    *   `notificationService?.getDeviceToken()`
    *   `notificationService?.isTokenRefresh(context)`
    *   `FirebaseMessaging.instance.getInitialMessage()`
    *   The `didChangeAppLifecycleState` method.
    *   The `checkDeepLink` method.
*   **New Location/Strategy:**
    1.  A new **`DeepLinkService`** should be created to manage listening for and processing deep links. It can hold the link state.
    2.  The **`NotificationService`** should be initialized once when the app starts (e.g., in `main.dart`).
    3.  When `NotificationService` receives a notification that contains a deep link, it should pass the link to the `DeepLinkService` to be handled. The `DeepLinkService` can then use `GoRouter` to navigate to the correct screen.

### App Startup Logic

The following logic should be executed once during the app's initialization phase (e.g., on the splash screen) rather than in the `HomeScreen`.

*   **Code to be Removed from `_HomeScreenState`:**
    *   `Provider.of<InAppPurchaseController>(...).fetchProducts(context)`
    *   `isActiveSubscription()`
    *   `checkFirstTimeUser()`
    *   `Provider.of<LoginController>(...).updateFcmToken()`
    *   `checkForUpdates()`
*   **New Location/Strategy:**
    *   This logic should be moved to a provider associated with the splash screen (e.g., `SplashProvider`). This provider can run all these initialization tasks, and once they are complete, it can navigate the user to the `HomeScreen` or `LoginScreen`. This ensures all necessary data is loaded and checks are performed before the user interacts with the main app.