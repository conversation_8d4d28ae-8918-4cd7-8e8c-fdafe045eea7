import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/app/features/messaging/providers/message_provider.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/views/clubs/clubs_home/providers/clubs_home_provider.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

/// Widget for displaying club invitations buttons
class ClubInvitationsSection extends StatelessWidget {
  const ClubInvitationsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ClubsHomeProvider>(
      builder: (context, provider, child) {
        return Column(
          children: [
            _buildIncomingInvitationsButton(context, provider),
            const SizedBox(height: 25),
            _buildOutgoingRequestsButton(context, provider),
          ],
        );
      },
    );
  }

  /// Build incoming club invitations button
  Widget _buildIncomingInvitationsButton(
      BuildContext context, ClubsHomeProvider provider) {
    return Consumer<MessageProvider>(
      builder: (context, messageController, child) {
        return Stack(
          clipBehavior: Clip.none,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: NetworkAwareTap(
                onTap: () {
                  context.pushNamed(
                    'club-invitations',
                    queryParameters: {
                      'userId': provider.loggedInUserId.toString(),
                    },
                  );
                },
                child: Skeleton.replace(
                  replacement: _buildButtonSkeleton(
                      context, "Incoming Club Invitations"),
                  child: Container(
                    constraints: BoxConstraints(
                      minHeight: 45,
                      maxWidth: MediaQuery.of(context).size.width,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(49),
                      color: AppConstants.textGreenColor,
                    ),
                    child: IntrinsicHeight(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20.0,
                          vertical: 12.0,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Flexible(
                              child: Text(
                                "Incoming Club Invitations",
                                textAlign: TextAlign.center,
                                style: lbBold.copyWith(fontSize: 18),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const SizedBox(width: 15),
                            Image.asset(
                              AppConstants.openToInvitationImagePath,
                              height: 20,
                              width: 20,
                              filterQuality: FilterQuality.high,
                              fit: BoxFit.contain,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
            // Notification indicator
            if (messageController.isPendingInvitation)
              Positioned(
                top: -8,
                right: 40,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(49),
                  child: Image.asset(
                    AppConstants.notificationImagePath,
                    height: 15,
                    width: 15,
                    filterQuality: FilterQuality.high,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  /// Build outgoing club requests button
  Widget _buildOutgoingRequestsButton(
      BuildContext context, ClubsHomeProvider provider) {
    return Consumer<MessageProvider>(
      builder: (context, messageController, child) {
        return Stack(
          clipBehavior: Clip.none,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: NetworkAwareTap(
                onTap: () {
                  context.pushNamed('club-request');
                },
                child: Skeleton.replace(
                  replacement:
                      _buildButtonSkeleton(context, "Outgoing Club Requests"),
                  child: Container(
                    constraints: BoxConstraints(
                      minHeight: 45,
                      maxWidth: MediaQuery.of(context).size.width,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(49),
                      color: AppConstants.textGreenColor,
                    ),
                    child: IntrinsicHeight(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20.0,
                          vertical: 12.0,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Flexible(
                              child: Text(
                                "Outgoing Club Requests",
                                textAlign: TextAlign.center,
                                style: lbBold.copyWith(fontSize: 18),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const SizedBox(width: 15),
                            Image.asset(
                              'assets/icons/Requests.png',
                              height: 25,
                              width: 25,
                              filterQuality: FilterQuality.high,
                              fit: BoxFit.contain,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
            // Notification indicator
            if (messageController.isOutgoingRequest)
              Positioned(
                top: -8,
                right: 40,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(49),
                  child: Image.asset(
                    AppConstants.notificationImagePath,
                    height: 15,
                    width: 15,
                    filterQuality: FilterQuality.high,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  /// Build skeleton button for loading state
  Widget _buildButtonSkeleton(BuildContext context, String text) {
    return Container(
      constraints: BoxConstraints(
        minHeight: 45,
        maxWidth: MediaQuery.of(context).size.width,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(49),
        color: AppConstants.skeletonBackgroundColor,
      ),
      child: IntrinsicHeight(
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 20.0,
            vertical: 12.0,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Flexible(
                child: Text(
                  text,
                  textAlign: TextAlign.center,
                  style: lbBold.copyWith(fontSize: 18),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(width: 15),
              Container(
                height: 20,
                width: 20,
                decoration: BoxDecoration(
                  color: AppConstants.skeletonforgroundColor,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
