import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/enums/enums.dart';
import 'package:eljunto/app/core/helpers/datetime_helper.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/models/home_model/home_screen1_model/new_club_opening_model.dart';
import 'package:eljunto/reusableWidgets/marquee_text.dart';
import 'package:flutter/material.dart';

class NewClubOpeningCard extends StatelessWidget {
  final NewClubOpeningList club;
  final VoidCallback onMatchTap;

  const NewClubOpeningCard(
      {super.key, required this.club, required this.onMatchTap});

  @override
  Widget build(BuildContext context) {
    String meetingTime = '';
    String? authorName = '';
    String? clubCount = '';
    String? selectedBook = '';
    String? selectedBookAuthor = '';
    String? partCovered = '';

    partCovered = club.latestPartsOfBookCovered ?? '';

    if (club.clubType == ClubType.impromptu.value) {
      authorName = club.latestBookAuthor;
      clubCount = club.clubCount ?? '';
    } else if (club.clubType == ClubType.standing.value) {
      selectedBook = club.latestBookName;
      selectedBookAuthor = club.latestBookAuthor;
    }

    int currentDate = DateTime.now().millisecondsSinceEpoch;

    String meetingHeading = ((club.latestMeetingDate) != null &&
            (club.latestMeetingDate ?? 0) <= currentDate)
        ? 'Previous Meeting:'
        : ((club.latestBookName != null))
            ? 'Next Meeting:'
            : 'First meeting not yet scheduled';
    String formattedDate = (meetingHeading == 'Next Meeting:')
        ? DateTimeHelper.getDayMonthYearDateFormat(club.latestMeetingDate)
        : (meetingHeading == 'Previous Meeting:')
            ? DateTimeHelper.getDayMonthYearDateFormat(club.latestMeetingDate)
            : '';

    meetingTime = DateTimeHelper.getMeetingScheduleTime(
        club.latestMeetingStartTime ?? 0, club.latestMeetingEndTime ?? 0);

    return Container(
      padding: const EdgeInsets.all(14),
      margin: const EdgeInsets.only(left: 10),
      width: 320,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Wrap the text in a Flexible to prevent overflow
              Flexible(
                fit: FlexFit.tight,
                flex: 22,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    MarqueeList(
                      children: [
                        Text(
                          club.clubName ?? '',
                          overflow: TextOverflow.ellipsis,
                          style: lbBold.copyWith(
                            fontSize: 18,
                          ),
                        ),
                      ],
                    ),
                    if (club.clubType == ClubType.impromptu.value &&
                        (authorName?.isNotEmpty ?? false) &&
                        (clubCount.isNotEmpty)) ...[
                      Text(
                        authorName ?? '',
                        overflow: TextOverflow.ellipsis,
                        style: lbRegular.copyWith(
                          fontSize: 12,
                        ),
                      ),
                      const SizedBox(height: 5),
                      Text(
                        clubCount,
                        overflow: TextOverflow.ellipsis,
                        style: lbItalic.copyWith(
                          fontSize: 12,
                        ),
                      ),
                    ] else ...[
                      const SizedBox.shrink(),
                    ],
                  ],
                ),
              ),
              const SizedBox(width: 5),
              const Spacer(),
              // Ensure the image does not exceed space
              SizedBox(
                height: 50,
                width: 50,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(49),
                  child: Image.asset(
                    AppConstants.clubOpeningLogoImagePath,
                    fit: BoxFit.cover,
                    filterQuality: FilterQuality.high,
                  ),
                ),
              ),
            ],
          ),
          (club.clubType == ClubType.standing.value)
              ? const SizedBox(height: 0)
              : const SizedBox(height: 25),
          (club.clubType == ClubType.impromptu.value)
              ? const SizedBox(
                  height: 4,
                )
              : const SizedBox.shrink(),
          Text(
            meetingHeading,
            overflow: TextOverflow.ellipsis,
            style: lbRegular.copyWith(fontSize: 12),
          ),
          if (club.clubType == ClubType.standing.value) ...[
            const SizedBox(
              height: 5,
            ),
            MarqueeList(
              children: [
                Text(
                  selectedBook ?? '',
                  overflow: TextOverflow.ellipsis,
                  style: lbBold.copyWith(
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 5,
            ),
            Text(
              selectedBookAuthor ?? '',
              overflow: TextOverflow.ellipsis,
              style: lbRegular.copyWith(fontSize: 12),
            ),
          ] else ...[
            const SizedBox.shrink(),
          ],
          const SizedBox(height: 5),
          Text(
            partCovered,
            overflow: TextOverflow.ellipsis,
            style: lbRegular.copyWith(fontSize: 12),
          ),
          const SizedBox(height: 5),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    formattedDate,
                    overflow: TextOverflow.ellipsis,
                    style: lbItalic.copyWith(fontSize: 12),
                  ),
                  const SizedBox(
                    height: 5,
                  ),
                  Text(
                    (meetingHeading == 'First meeting not yet scheduled')
                        ? ''
                        : meetingTime,
                    overflow: TextOverflow.ellipsis,
                    style: lbItalic.copyWith(fontSize: 12),
                  ),
                ],
              ),
              const Spacer(),
              GestureDetector(
                onTap: onMatchTap,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    color: ((club.isCurrentlyReadingMatch ?? 0) > 0)
                        ? AppConstants.textGreenColor
                        : AppConstants.isActiveRequestColor,
                  ),
                  child: Text(
                    ((club.isCurrentlyReadingMatch ?? 0) == 0)
                        ? "No Match"
                        : ((club.isCurrentlyReadingMatch ?? 0) > 0 &&
                                (club.isCurrentlyReadingMatch ?? 0) < 2)
                            ? "${club.isCurrentlyReadingMatch ?? 0} Match"
                            : "${club.isCurrentlyReadingMatch ?? 0} Matches",
                    overflow: TextOverflow.ellipsis,
                    style: lbBold.copyWith(
                      fontSize: 12,
                      color: ((club.isCurrentlyReadingMatch ?? 0) > 0)
                          ? AppConstants.primaryColor
                          : Colors.black38,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
