import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/app/features/home/<USER>/home_provider.dart';
import 'package:eljunto/app/features/home/<USER>/widgets/fellow_reader_card.dart';
import 'package:eljunto/models/home_model/home_screen1_model/fellow_reader_model.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/matches_bottom_sheet/matches_bottom_sheet.dart';
import 'package:eljunto/reusableWidgets/no_data_widget.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

class FellowReadersList extends StatefulWidget {
  const FellowReadersList({super.key});

  @override
  State<FellowReadersList> createState() => _FellowReadersListState();
}

class _FellowReadersListState extends State<FellowReadersList>
    with TickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  final List<GlobalKey> _itemKeys = [];
  final ValueNotifier<double> _maxHeightNotifier = ValueNotifier(0.0);
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _maxHeightNotifier.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _initializeKeys(int length) {
    _itemKeys.clear();
    for (int i = 0; i < length; i++) {
      _itemKeys.add(GlobalKey());
    }
  }

  void _onScroll() {
    final provider = Provider.of<HomeProvider>(context, listen: false);
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent &&
        !provider.isLoadingMoreReaders &&
        provider.fellowReadersList.length < provider.fellowReadersCount) {
      provider.loadMoreReaders();
    }
  }

  void _calculateMaxHeight() {
    if (!mounted) return;

    double maxHeight = 0.0;
    for (var key in _itemKeys) {
      try {
        final context = key.currentContext;
        if (context != null && mounted) {
          final renderBox = context.findRenderObject() as RenderBox?;
          if (renderBox != null && renderBox.hasSize) {
            final height = renderBox.size.height;
            maxHeight = maxHeight < height ? height : maxHeight;
          }
        }
      } catch (e) {
        continue;
      }
    }

    if (maxHeight != _maxHeightNotifier.value && mounted) {
      setState(() {
        _maxHeightNotifier.value = maxHeight;
      });
    }
  }

  Future<void> _onRefresh() async {
    _animationController.reset();
    _animationController.forward();

    final provider = Provider.of<HomeProvider>(context, listen: false);
    if (provider.fellowReadersList.isNotEmpty) {
      await _scrollController.position.animateTo(
        0.0,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeOut,
      );
    }
    await provider.refreshReaders();
  }

  Future<void> _showFellowReaderMatchBottomSheet(
      FellowReader fellowReader) async {
    return showModalBottomSheet(
      isScrollControlled: true,
      useRootNavigator: true,
      barrierColor: AppConstants.backgroundColor.withValues(alpha: .5),
      backgroundColor: Colors.transparent,
      context: context,
      builder: (context) {
        return FellowReaderMatchesBottomSheet(
          currentlyReadMatch: fellowReader.currentlyReadingBooks ?? [],
          fivestarMatch: fellowReader.fiveStarMatchBooks ?? [],
          tobeReadMatch: fellowReader.toBeReadBooks ?? [],
          userName: fellowReader.userName ?? '',
          userId: fellowReader.userId ?? 0,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<HomeProvider>(
      builder: (context, provider, child) {
        if (provider.fellowReadersList.isEmpty && !provider.isLoading) {
          return Skeleton.replace(
            replacement: _messageSkeleton("No fellow readers"),
            child: const NoDataWidget(
              message: "No fellow readers",
            ),
          );
        }

        _initializeKeys(provider.fellowReadersList.length);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Skeleton.replace(
              replacement: Divider(
                thickness: 2,
                color: AppConstants.skeletonBackgroundColor,
              ),
              child: Divider(
                thickness: 2,
                color: AppConstants.primaryColor,
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20.0, top: 15),
              child: Row(
                children: [
                  Text(
                    "Fellow Readers",
                    overflow: TextOverflow.ellipsis,
                    style: lbBold.copyWith(
                      fontSize: 18,
                    ),
                  ),
                  const SizedBox(width: 10),
                  RotationTransition(
                    turns: Tween(begin: 0.0, end: 1.0)
                        .animate(_animationController),
                    child: NetworkAwareTap(
                      onTap: _onRefresh,
                      child: const Icon(
                        Icons.autorenew_rounded,
                        size: 25,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 10),
            SingleChildScrollView(
              padding: const EdgeInsets.only(left: 10.0, right: 20),
              scrollDirection: Axis.horizontal,
              controller: _scrollController,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: List.generate(
                  provider.isLoadingMoreReaders
                      ? provider.fellowReadersList.length + 1
                      : provider.fellowReadersList.length,
                  (index) {
                    if (index == provider.fellowReadersList.length &&
                        provider.isLoadingMoreReaders) {
                      return Padding(
                        padding: const EdgeInsets.only(left: 10.0),
                        child: Container(
                          padding: const EdgeInsets.only(left: 10.0),
                          height: _maxHeightNotifier.value,
                          child: const Center(
                            child: CircularProgressIndicator(
                              color: AppConstants.primaryColor,
                            ),
                          ),
                        ),
                      );
                    }

                    final fellowReader = provider.fellowReadersList[index];
                    final userImage = ApiConstants.imageBaseUrl +
                        (fellowReader.userProfilePicture ?? '');

                    return NetworkAwareTap(
                      onTap: () {
                        context.pushNamed(
                          'club-member-profile',
                          extra: {
                            'userId': fellowReader.userId,
                            'userName': fellowReader.userName,
                          },
                        );
                      },
                      child: ValueListenableBuilder(
                        valueListenable: _maxHeightNotifier,
                        builder: (context, maxHeight, _) {
                          return LayoutBuilder(
                            builder: (context, constraints) {
                              WidgetsBinding.instance.addPostFrameCallback(
                                (_) {
                                  if (mounted) {
                                    _calculateMaxHeight();
                                  }
                                },
                              );

                              return ClipRect(
                                clipBehavior: Clip.antiAliasWithSaveLayer,
                                child: SizedBox(
                                  key: _itemKeys[index],
                                  // height: maxHeight > 0 ? maxHeight : null,
                                  child: FellowReaderCard(
                                    fellowReader: fellowReader,
                                    userImage: userImage,
                                    onMatchTap: () {
                                      // Handle match tap - show bottom sheet
                                      if ((fellowReader.totalMatches ?? 0) >
                                          0) {
                                        _showFellowReaderMatchBottomSheet(
                                            fellowReader);
                                      }
                                    },
                                  ),
                                ),
                              );
                            },
                          );
                        },
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _messageSkeleton(String message) {
    return Container(
      padding: const EdgeInsets.only(left: 20),
      margin: const EdgeInsets.symmetric(horizontal: 20),
      height: 200,
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        color: AppConstants.skeletonBackgroundColor,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Text(message, textAlign: TextAlign.start),
              Text(message, textAlign: TextAlign.start),
              Text(message, textAlign: TextAlign.start),
              Text(message, textAlign: TextAlign.start),
            ],
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                margin: const EdgeInsets.all(14),
                height: 50,
                width: 50,
                decoration: BoxDecoration(
                  color: AppConstants.skeletonforgroundColor,
                  borderRadius: BorderRadius.circular(50),
                ),
              ),
              Container(
                margin: const EdgeInsets.all(14),
                height: 45,
                width: 80,
                decoration: BoxDecoration(
                  color: AppConstants.skeletonforgroundColor,
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
