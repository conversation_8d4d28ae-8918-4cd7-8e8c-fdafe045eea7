import 'package:flutter/material.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../../../reusableWidgets/cached_network_image.dart';
import '../../../../../reusableWidgets/marquee_text.dart';
import '../../../../core/constants.dart';
import '../../../../core/utils/text_style.dart';
import '../../../../core/widgets/app_button.dart';

class FellowReaderSkeleton extends StatelessWidget {
  const FellowReaderSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      containersColor: Color.fromRGBO(
        224,
        224,
        224,
        1,
      ),
      effect: SoldColorEffect(
        lowerBound: 0.1,
        upperBound: 0.5,
      ),
      child: SizedBox(
        height: 280,
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: 10,
          itemBuilder: (context, index) => Container(
            margin: const EdgeInsets.only(left: 10),
            width: 280,
            decoration: BoxDecoration(
              color: AppConstants.skeletonBackgroundColor,
              borderRadius: BorderRadius.circular(28),
              border: Border.all(color: AppConstants.primaryColor, width: 1),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    spacing: 10,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            MarqueeList(
                              children: [
                                Text(
                                  'fellowReader.userName',
                                  overflow: TextOverflow.ellipsis,
                                  style: lbBold.copyWith(fontSize: 16),
                                ),
                              ],
                            ),
                            const SizedBox(height: 3),
                            Text(
                              'fellowReader.userLocation',
                              overflow: TextOverflow.ellipsis,
                              style: lbBold.copyWith(fontSize: 12),
                            ),
                          ],
                        ),
                      ),
                      ClipRRect(
                        borderRadius: BorderRadius.circular(30),
                        child: CustomCachedNetworkImage(
                          imageUrl: 'userImage',
                          width: 44,
                          height: 44,
                          errorImage: AppConstants.profileLogoImagePath,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    "No book in currently reading",
                    overflow: TextOverflow.ellipsis,
                    style: lbRegular.copyWith(fontSize: 12),
                  ),
                  const SizedBox(height: 5),
                  MarqueeList(
                    children: [
                      Text(
                        'fellowReader.bookName',
                        overflow: TextOverflow.ellipsis,
                        style: lbBold.copyWith(fontSize: 12),
                      ),
                    ],
                  ),
                  MarqueeList(
                    children: [
                      Text(
                        'fellowReader.bookAuthor',
                        overflow: TextOverflow.ellipsis,
                        style: lbRegular.copyWith(fontSize: 12),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  AppButton(
                    text: "No Match",
                    height: 44,
                    borderRadius: 16,
                    textSize: 12,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
