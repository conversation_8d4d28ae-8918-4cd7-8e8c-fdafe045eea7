import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/models/home_model/home_screen1_model/fellow_reader_model.dart';
import 'package:eljunto/reusableWidgets/cached_network_image.dart';
import 'package:eljunto/reusableWidgets/marquee_text.dart';
import 'package:flutter/material.dart';

class FellowReaderCard extends StatelessWidget {
  final FellowReader fellowReader;
  final VoidCallback onMatchTap;
  final String userImage;

  const FellowReaderCard({
    super.key,
    required this.fellowReader,
    required this.onMatchTap,
    required this.userImage,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(left: 10),
      width: 320,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(14.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      MarqueeList(
                        children: [
                          Text(
                            fellowReader.userName ?? '',
                            overflow: TextOverflow.ellipsis,
                            style: lbBold.copyWith(
                              fontSize: 18,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: 3,
                      ),
                      Text(
                        fellowReader.userLocation ?? '',
                        overflow: TextOverflow.ellipsis,
                        style: lbBold.copyWith(
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                ClipRRect(
                  borderRadius: BorderRadius.circular(49),
                  child: CustomCachedNetworkImage(
                    imageUrl: userImage,
                    width: 45,
                    height: 45,
                    errorImage: AppConstants.profileLogoImagePath,
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 15,
            ),
            Text(
              (fellowReader.bookName?.isNotEmpty ?? false)
                  ? "Currently Reading:"
                  : "No book in currently reading",
              overflow: TextOverflow.ellipsis,
              style: lbRegular.copyWith(
                fontSize: 12,
              ),
            ),
            const SizedBox(
              height: 15,
            ),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      MarqueeList(
                        children: [
                          Text(
                            fellowReader.bookName ?? '',
                            overflow: TextOverflow.ellipsis,
                            style: lbBold.copyWith(
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: 5,
                      ),
                      MarqueeList(
                        children: [
                          Text(
                            fellowReader.bookAuthor ?? '',
                            overflow: TextOverflow.ellipsis,
                            style: lbRegular.copyWith(
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 15),
                GestureDetector(
                  onTap: onMatchTap,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      color: ((fellowReader.totalMatches ?? 0) > 0)
                          ? AppConstants.textGreenColor
                          : AppConstants.isActiveRequestColor,
                    ),
                    child: Text(
                      ((fellowReader.totalMatches ?? 0) == 0)
                          ? "No Match"
                          : ((fellowReader.totalMatches ?? 0) > 0 &&
                                  (fellowReader.totalMatches ?? 0) < 2)
                              ? "${fellowReader.totalMatches ?? 0} Match"
                              : "${fellowReader.totalMatches ?? 0} Matches",
                      overflow: TextOverflow.ellipsis,
                      style: lbBold.copyWith(
                        fontSize: 12,
                        color: ((fellowReader.totalMatches ?? 0) > 0)
                            ? AppConstants.primaryColor
                            : Colors.black38,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
