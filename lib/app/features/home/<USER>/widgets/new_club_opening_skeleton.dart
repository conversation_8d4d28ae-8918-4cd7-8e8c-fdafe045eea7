import 'package:eljunto/app/core/widgets/app_button.dart';
import 'package:flutter/material.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../../../reusableWidgets/marquee_text.dart';
import '../../../../core/constants.dart';
import '../../../../core/utils/text_style.dart';

class NewClubOpeningSkeleton extends StatelessWidget {
  const NewClubOpeningSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      containersColor: Color.fromRGBO(
        224,
        224,
        224,
        1,
      ),
      effect: SoldColorEffect(
        lowerBound: 0.1,
        upperBound: 0.5,
      ),
      child: SizedBox(
        height: 300,
        child: ListView.builder(
          itemCount: 3,
          shrinkWrap: true,
          scrollDirection: Axis.horizontal,
          itemBuilder: (context, index) => Container(
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.only(left: 10),
            width: 280,
            decoration: BoxDecoration(
              color: AppConstants.skeletonBackgroundColor,
              borderRadius: BorderRadius.circular(28),
              border: Border.all(
                color: AppConstants.primaryColor,
                width: 1,
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  spacing: 10,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Flexible(
                      fit: FlexFit.tight,
                      flex: 22,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          MarqueeList(
                            children: [
                              Text(
                                "club.clubName",
                                overflow: TextOverflow.ellipsis,
                                style: lbBold.copyWith(fontSize: 16),
                              ),
                            ],
                          ),
                          Text(
                            "authorName",
                            overflow: TextOverflow.ellipsis,
                            style: lbRegular.copyWith(fontSize: 12),
                          ),
                          const SizedBox(height: 5),
                          Text(
                            "club.clubType",
                            overflow: TextOverflow.ellipsis,
                            style: lbItalic.copyWith(fontSize: 12),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 48,
                      width: 48,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(49),
                        child: Image.asset(
                          AppConstants.clubOpeningLogoImagePath,
                          fit: BoxFit.cover,
                          filterQuality: FilterQuality.high,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  "meetingHeading",
                  overflow: TextOverflow.ellipsis,
                  style: lbRegular.copyWith(fontSize: 12),
                ),
                const SizedBox(height: 5),
                MarqueeList(
                  children: [
                    Text(
                      'selectedBook',
                      overflow: TextOverflow.ellipsis,
                      style: lbBold.copyWith(fontSize: 12),
                    ),
                  ],
                ),
                const SizedBox(height: 5),
                Text(
                  'selectedBookAuthor',
                  overflow: TextOverflow.ellipsis,
                  style: lbRegular.copyWith(fontSize: 12),
                ),
                const SizedBox(height: 5),
                Text(
                  'partCovered',
                  overflow: TextOverflow.ellipsis,
                  style: lbRegular.copyWith(fontSize: 12),
                ),
                const SizedBox(height: 5),
                Text(
                  'formattedDate',
                  overflow: TextOverflow.ellipsis,
                  style: lbItalic.copyWith(fontSize: 12),
                ),
                const SizedBox(height: 5),
                Text(
                  'First meeting not yet scheduled',
                  overflow: TextOverflow.ellipsis,
                  style: lbItalic.copyWith(fontSize: 12),
                ),
                const SizedBox(height: 16),
                AppButton(
                  text: 'No Match',
                  height: 44,
                  borderRadius: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
