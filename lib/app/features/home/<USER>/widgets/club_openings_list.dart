import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/app/features/home/<USER>/home_provider.dart';
import 'package:eljunto/app/features/home/<USER>/widgets/new_club_opening_card.dart';
import 'package:eljunto/app/features/home/<USER>/widgets/new_club_opening_skeleton.dart';
import 'package:eljunto/models/home_model/home_screen1_model/new_club_opening_model.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/no_data_widget.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../../../reusableWidgets/matches_bottom_sheet/club_opening_matches_sheet.dart';

class ClubOpeningsList extends StatefulWidget {
  const ClubOpeningsList({super.key});

  @override
  State<ClubOpeningsList> createState() => _ClubOpeningsListState();
}

class _ClubOpeningsListState extends State<ClubOpeningsList>
    with TickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  final List<GlobalKey> _itemKeys = [];
  final ValueNotifier<double> _maxHeightNotifier = ValueNotifier(0.0);
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _maxHeightNotifier.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _initializeKeys(int length) {
    _itemKeys.clear();
    for (int i = 0; i < length; i++) {
      _itemKeys.add(GlobalKey());
    }
  }

  void _onScroll() {
    final provider = Provider.of<HomeProvider>(context, listen: false);
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent &&
        !provider.isLoadingMoreClubs &&
        provider.clubList.length < provider.clubOpeningCount) {
      provider.loadMoreClubs();
    }
  }

  void _calculateMaxHeight() {
    if (!mounted) return;

    double maxHeight = 0.0;
    for (var key in _itemKeys) {
      try {
        final context = key.currentContext;
        if (context != null && mounted) {
          final renderBox = context.findRenderObject() as RenderBox?;
          if (renderBox != null) {
            final height = renderBox.size.height;
            maxHeight = maxHeight < height ? height : maxHeight;
          }
        }
      } catch (e) {
        continue;
      }
    }

    if (maxHeight != _maxHeightNotifier.value && mounted) {
      _maxHeightNotifier.value = maxHeight;
    }
  }

  Future<void> _onRefresh() async {
    _animationController.reset();
    _animationController.forward();

    final provider = Provider.of<HomeProvider>(context, listen: false);
    if (provider.clubList.isNotEmpty) {
      await _scrollController.position.animateTo(
        0.0,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeOut,
      );
    }
    await provider.refreshClubs();
  }

  Future<void> _showClubMatchBottomSheet(NewClubOpeningList club) async {
    return showModalBottomSheet(
      barrierColor: Colors.white60,
      isScrollControlled: true,
      useRootNavigator: true,
      context: context,
      builder: (context) {
        return ClubOpeningMatchesBottomSheet(
          clubId: club.clubId ?? 0,
          clubName: club.clubName ?? '',
          memberRequestPrompt: club.memberRequestPrompt ?? '',
          matchedMeetings: club.matchedMeetings ?? [],
          clubList: club,
        );
      },
    ).then((_) {
      // Refresh data after bottom sheet closes
      final provider = Provider.of<HomeProvider>(context, listen: false);
      provider.refreshClubs();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<HomeProvider>(
      builder: (context, provider, child) {
        // Show skeleton placeholders during initial loading
        if (provider.isLoading || provider.clubList.isEmpty) {
          return NewClubOpeningSkeleton();
        }

        // Show no data message when not loading and list is empty
        if (provider.clubList.isEmpty && !provider.isLoading) {
          return Skeleton.replace(
            replacement: _messageSkeleton("No new club openings"),
            child: const NoDataWidget(
              message: "No new club openings",
            ),
          );
        }

        _initializeKeys(provider.clubList.length);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 20.0),
              child: Row(
                children: [
                  Text(
                    "New Club Openings",
                    overflow: TextOverflow.ellipsis,
                    style: lbBold.copyWith(fontSize: 18),
                  ),
                  const SizedBox(width: 10),
                  RotationTransition(
                    turns: Tween(begin: 0.0, end: 1.0)
                        .animate(_animationController),
                    child: NetworkAwareTap(
                      onTap: _onRefresh,
                      child: const Icon(
                        Icons.autorenew_rounded,
                        size: 25,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 10),
            SingleChildScrollView(
              padding: const EdgeInsets.only(left: 10, right: 20),
              scrollDirection: Axis.horizontal,
              controller: _scrollController,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: List.generate(
                  provider.isLoadingMoreClubs
                      ? provider.clubList.length + 1
                      : provider.clubList.length,
                  (index) {
                    if (index == provider.clubList.length &&
                        provider.isLoadingMoreClubs) {
                      return Padding(
                        padding: const EdgeInsets.only(left: 10.0),
                        child: Container(
                          padding: const EdgeInsets.only(left: 10.0),
                          height: _maxHeightNotifier.value,
                          child: const Center(
                            child: CircularProgressIndicator(
                              color: AppConstants.primaryColor,
                            ),
                          ),
                        ),
                      );
                    }

                    final club = provider.clubList[index];
                    return NetworkAwareTap(
                      onTap: () {
                        context.pushNamed(
                          'club-details',
                          extra: {
                            'bookClubId': club.clubId,
                            'bookClubName': club.clubName,
                            'impromptuCount': club.clubCount,
                          },
                        );
                      },
                      child: ValueListenableBuilder(
                        valueListenable: _maxHeightNotifier,
                        builder: (context, maxHeight, _) {
                          return LayoutBuilder(
                            builder: (context, constraints) {
                              if (mounted &&
                                  maxHeight < constraints.maxHeight) {
                                WidgetsBinding.instance.addPostFrameCallback(
                                  (_) {
                                    if (mounted) {
                                      _calculateMaxHeight();
                                    }
                                  },
                                );
                              }

                              return ClipRect(
                                clipBehavior: Clip.antiAliasWithSaveLayer,
                                child: SizedBox(
                                  key: _itemKeys[index],
                                  child: NewClubOpeningCard(
                                    club: club,
                                    onMatchTap: () {
                                      // Handle match tap - show bottom sheet
                                      if ((club.isCurrentlyReadingMatch ?? 0) >
                                          0) {
                                        _showClubMatchBottomSheet(club);
                                      }
                                    },
                                  ),
                                ),
                              );
                            },
                          );
                        },
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _messageSkeleton(String message) {
    return Container(
      padding: const EdgeInsets.only(left: 20),
      margin: const EdgeInsets.symmetric(horizontal: 20),
      height: 200,
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        color: AppConstants.skeletonBackgroundColor,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Text(message, textAlign: TextAlign.start),
              Text(message, textAlign: TextAlign.start),
              Text(message, textAlign: TextAlign.start),
              Text(message, textAlign: TextAlign.start),
            ],
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                margin: const EdgeInsets.all(14),
                height: 50,
                width: 50,
                decoration: BoxDecoration(
                  color: AppConstants.skeletonforgroundColor,
                  borderRadius: BorderRadius.circular(50),
                ),
              ),
              Container(
                margin: const EdgeInsets.all(14),
                height: 45,
                width: 80,
                decoration: BoxDecoration(
                  color: AppConstants.skeletonforgroundColor,
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
