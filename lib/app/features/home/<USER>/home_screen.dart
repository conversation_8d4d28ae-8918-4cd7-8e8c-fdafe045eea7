import 'package:eljunto/app/core/widgets/custom_scaffold.dart';
import 'package:eljunto/app/core/widgets/unified_app_bar.dart';
import 'package:eljunto/app/features/home/<USER>/home_provider.dart';
import 'package:eljunto/app/features/home/<USER>/widgets/club_openings_list.dart';
import 'package:eljunto/app/features/home/<USER>/widgets/fellow_readers_list.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final homeProvider = context.read<HomeProvider>();
      await homeProvider.fetchInitialData();
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      appBar: UnifiedAppBar.main(
        title: 'Home',
        showBackButton: false,
        showLogo: true,
      ),
      body: Consumer<HomeProvider>(
        builder: (context, provider, child) {
          // Don't apply Skeletonizer when showing custom skeleton content
          // Custom skeleton content is shown when isLoading=true and lists are empty
          bool showCustomSkeleton = provider.isLoading &&
              provider.clubList.isEmpty &&
              provider.fellowReadersList.isEmpty;

          return SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 25),
                const ClubOpeningsList(),
                const SizedBox(height: 25),
                const FellowReadersList(),
                const SizedBox(height: 25),
              ],
            ),
          );
        },
      ),
    );
  }
}
