import 'package:eljunto/app/features/home/<USER>/home_service.dart';
import 'package:eljunto/models/home_model/home_screen1_model/fellow_reader_model.dart';
import 'package:eljunto/models/home_model/home_screen1_model/new_club_opening_model.dart';
import 'package:flutter/material.dart';

class HomeProvider with ChangeNotifier {
  final HomeService _homeService;

  HomeProvider({required HomeService homeService}) : _homeService = homeService;

  bool _isLoading = false;
  bool get isLoading => _isLoading;

  bool _isLoadingMoreClubs = false;
  bool get isLoadingMoreClubs => _isLoadingMoreClubs;

  bool _isLoadingMoreReaders = false;
  bool get isLoadingMoreReaders => _isLoadingMoreReaders;

  List<NewClubOpeningList> _clubList = [];
  List<NewClubOpeningList> get clubList => _clubList;

  List<FellowReader> _fellowReadersList = [];
  List<FellowReader> get fellowReadersList => _fellowReadersList;

  int _clubPage = 1;
  int _readerPage = 1;
  final int _limit = 10;

  bool _hasMoreClubs = true;
  bool _hasMoreReaders = true;

  Future<void> fetchInitialData() async {
    _isLoading = true;
    notifyListeners();

    await Future.wait([
      _fetchNewClubOpenings(isRefresh: true),
      _fetchFellowReaders(isRefresh: true),
    ]);

    _isLoading = false;
    notifyListeners();
  }

  Future<void> _fetchNewClubOpenings({bool isRefresh = false}) async {
    if (isRefresh) {
      _clubPage = 1;
      _clubList = [];
      _hasMoreClubs = true;
    }

    if (_isLoadingMoreClubs || !_hasMoreClubs) return;

    _isLoadingMoreClubs = true;
    notifyListeners();

    try {
      final newClubs =
          await _homeService.getNewClubOpenings(page: _clubPage, limit: _limit);
      if (newClubs.length < _limit) {
        _hasMoreClubs = false;
      }
      _clubList.addAll(newClubs);
      _clubPage++;
    } catch (e) {
      // Handle error
    } finally {
      _isLoadingMoreClubs = false;
      notifyListeners();
    }
  }

  Future<void> _fetchFellowReaders({bool isRefresh = false}) async {
    if (isRefresh) {
      _readerPage = 1;
      _fellowReadersList = [];
      _hasMoreReaders = true;
    }

    if (_isLoadingMoreReaders || !_hasMoreReaders) return;

    _isLoadingMoreReaders = true;
    notifyListeners();

    try {
      final newReaders =
          await _homeService.getFellowReaders(page: _readerPage, limit: _limit);
      if (newReaders.length < _limit) {
        _hasMoreReaders = false;
      }
      _fellowReadersList.addAll(newReaders);
      _readerPage++;
    } catch (e) {
      // Handle error
    } finally {
      _isLoadingMoreReaders = false;
      notifyListeners();
    }
  }

  Future<void> loadMoreClubs() async {
    await _fetchNewClubOpenings();
  }

  Future<void> loadMoreReaders() async {
    await _fetchFellowReaders();
  }

  Future<void> refreshClubs() async {
    await _fetchNewClubOpenings(isRefresh: true);
  }

  Future<void> refreshReaders() async {
    await _fetchFellowReaders(isRefresh: true);
  }
}
