import 'package:eljunto/app/features/home/<USER>/home_service.dart';
import 'package:eljunto/models/home_model/home_screen1_model/fellow_reader_model.dart';
import 'package:eljunto/models/home_model/home_screen1_model/new_club_opening_model.dart';
import 'package:flutter/material.dart';

import '../../../core/services/setup_locator.dart';

class HomeProvider with ChangeNotifier {
  final _homeService = locator<HomeService>();

  bool _isLoading = false;

  bool get isLoading => _isLoading;

  bool _isLoadingMoreClubs = false;

  bool get isLoadingMoreClubs => _isLoadingMoreClubs;

  bool _isLoadingMoreReaders = false;

  bool get isLoadingMoreReaders => _isLoadingMoreReaders;

  List<NewClubOpeningList> _clubList = [];

  List<NewClubOpeningList> get clubList => _clubList;

  List<FellowReader> _fellowReadersList = [];

  List<FellowReader> get fellowReadersList => _fellowReadersList;

  // Match original implementation pagination logic
  int _clubOffset = 0;
  int _readerOffset = 0;
  int _clubOpeningLimit = 10;
  int _fellowReadersLimit = 10;

  int _clubOpeningCount = 0;

  int get clubOpeningCount => _clubOpeningCount;

  int _fellowReadersCount = 0;

  int get fellowReadersCount => _fellowReadersCount;

  Future<void> fetchInitialData() async {
    _isLoading = true;
    notifyListeners();

    await Future.wait(
      [
        _fetchNewClubOpenings(isRefresh: true),
        _fetchFellowReaders(isRefresh: true),
      ],
    );

    _isLoading = false;
    notifyListeners();
  }

  Future<void> _fetchNewClubOpenings({bool isRefresh = false}) async {
    if (isRefresh) {
      _clubOffset = 0;
      _clubOpeningLimit = 10;
      _clubList = [];
    }

    if (_isLoadingMoreClubs) return;

    _isLoadingMoreClubs = true;

    try {
      final newClubs = await _homeService.getNewClubOpenings(
          offset: _clubOffset, limit: _clubOpeningLimit);

      if (isRefresh) {
        _clubList = newClubs;
        // Get the total count for pagination
        _clubOpeningCount = await _homeService.getClubOpeningCount();
      } else {
        _clubList.addAll(newClubs);
      }
    } catch (e) {
      // Handle error
    } finally {
      _isLoadingMoreClubs = false;
      notifyListeners();
    }
  }

  Future<void> _fetchFellowReaders({bool isRefresh = false}) async {
    if (isRefresh) {
      _readerOffset = 0;
      _fellowReadersLimit = 10;
      _fellowReadersList = [];
    }

    if (_isLoadingMoreReaders) return;

    _isLoadingMoreReaders = true;

    try {
      final newReaders = await _homeService.getFellowReaders(
          offset: _readerOffset, limit: _fellowReadersLimit);

      if (isRefresh) {
        _fellowReadersList = newReaders;
        // Get the total count for pagination
        _fellowReadersCount = await _homeService.getFellowReadersCount();
      } else {
        _fellowReadersList.addAll(newReaders);
      }
    } catch (e) {
      // Handle error
    } finally {
      _isLoadingMoreReaders = false;
      notifyListeners();
    }
  }

  Future<void> loadMoreClubs() async {
    if (_clubList.length < _clubOpeningCount) {
      _clubOpeningLimit += 10;
      await _fetchNewClubOpenings();
    }
  }

  Future<void> loadMoreReaders() async {
    if (_fellowReadersList.length < _fellowReadersCount) {
      _fellowReadersLimit += 10;
      await _fetchFellowReaders();
    }
  }

  Future<void> refreshClubs() async {
    await _fetchNewClubOpenings(isRefresh: true);
  }

  Future<void> refreshReaders() async {
    await _fetchFellowReaders(isRefresh: true);
  }
}
