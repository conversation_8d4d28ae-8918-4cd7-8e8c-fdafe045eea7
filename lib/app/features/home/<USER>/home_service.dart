import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/network/http/http_service.dart';
import 'package:eljunto/app/core/services/session_manager.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/models/home_model/home_screen1_model/fellow_reader_model.dart';
import 'package:eljunto/models/home_model/home_screen1_model/new_club_opening_model.dart';

class HomeService {
  final _apiService = locator<HttpApiService>();
  final SessionManager _sessionManager = locator<SessionManager>();
  final String _baseURL = ApiConstants.flavorBaseUrl;

  Future<List<NewClubOpeningList>> getNewClubOpenings(
      {int offset = 0, int limit = 10}) async {
    final userId = _sessionManager.userId;
    final response = await _apiService.get(
      '$_baseURL/home/<USER>/list?userId=$userId&offset=$offset&limit=$limit',
    );

    if (response.statusCode == 200) {
      final newClubOpenModel = NewClubOpenModel.fromJson(response.data);
      return newClubOpenModel.data ?? [];
    } else {
      throw Exception('Failed to load new club openings');
    }
  }

  Future<List<FellowReader>> getFellowReaders(
      {int offset = 0, int limit = 10}) async {
    final userId = _sessionManager.userId;
    final Map<String, dynamic> payload = {
      "userId": userId,
      "offset": offset,
      "limit": limit,
    };

    final response = await _apiService.post(
      '$_baseURL/home/<USER>/list',
      payload,
    );

    if (response.statusCode == 200) {
      final fellowReaderModel = FellowReaderModel.fromJson(response.data);
      return fellowReaderModel.data ?? [];
    } else {
      throw Exception('Failed to load fellow readers');
    }
  }

  /// Get club opening count for pagination
  Future<int> getClubOpeningCount() async {
    final userId = _sessionManager.userId;
    final response = await _apiService.get(
      '$_baseURL/home/<USER>/list?userId=$userId&offset=0&limit=1',
    );

    if (response.statusCode == 200) {
      final newClubOpenModel = NewClubOpenModel.fromJson(response.data);
      return newClubOpenModel.count ?? 0;
    } else {
      return 0;
    }
  }

  /// Get fellow readers count for pagination
  Future<int> getFellowReadersCount() async {
    final userId = _sessionManager.userId;
    final Map<String, dynamic> payload = {
      "userId": userId,
      "offset": 0,
      "limit": 1,
    };

    final response = await _apiService.post(
      '$_baseURL/home/<USER>/list',
      payload,
    );

    if (response.statusCode == 200) {
      final fellowReaderModel = FellowReaderModel.fromJson(response.data);
      return fellowReaderModel.count ?? 0;
    } else {
      return 0;
    }
  }
}
