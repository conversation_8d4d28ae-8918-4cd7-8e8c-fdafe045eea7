import 'package:eljunto/app/core/network/http/http_service.dart';
import 'package:eljunto/app/core/services/session_manager.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/models/home_model/home_screen1_model/fellow_reader_model.dart';
import 'package:eljunto/models/home_model/home_screen1_model/new_club_opening_model.dart';

class HomeService {
  final HttpApiService _httpService;
  final SessionManager _sessionManager = locator<SessionManager>();

  HomeService({required HttpApiService httpService})
      : _httpService = httpService;

  Future<List<NewClubOpeningList>> getNewClubOpenings(
      {int page = 1, int limit = 10}) async {
    final userId = _sessionManager.userId;
    final response = await _httpService.post(
      '/clubs/new-openings',
      {
        "userId": userId,
        "offset": (page - 1) * limit,
        "limit": limit,
      },
    );

    if (response.statusCode == 200) {
      final newClubOpenModel = NewClubOpenModel.fromJson(response.data);
      return newClubOpenModel.data ?? [];
    } else {
      throw Exception('Failed to load new club openings');
    }
  }

  Future<List<FellowReader>> getFellowReaders(
      {int page = 1, int limit = 10}) async {
    final userId = _sessionManager.userId;
    final response = await _httpService.post(
      '/users/fellow-readers',
      {
        "userId": userId,
        "offset": (page - 1) * limit,
        "limit": limit,
      },
    );

    if (response.statusCode == 200) {
      final fellowReaderModel = FellowReaderModel.fromJson(response.data);
      return fellowReaderModel.data ?? [];
    } else {
      throw Exception('Failed to load fellow readers');
    }
  }
}
