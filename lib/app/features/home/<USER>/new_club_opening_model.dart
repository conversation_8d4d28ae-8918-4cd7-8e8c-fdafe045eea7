// To parse this JSON data, do
//
//     final newClubOpenModel = newClubOpenModelFromJson(jsonString);

import 'dart:convert';

NewClubOpenModel newClubOpenModelFromJson(String str) =>
    NewClubOpenModel.fromJson(json.decode(str));

String newClubOpenModelToJson(NewClubOpenModel data) =>
    json.encode(data.toJson());

class NewClubOpenModel {
  final String? message;
  final List<NewClubOpeningList>? data;
  final int? count;
  final int? statusCode;

  NewClubOpenModel({
    this.message,
    this.data,
    this.count,
    this.statusCode,
  });

  factory NewClubOpenModel.fromJson(Map<String, dynamic> json) =>
      NewClubOpenModel(
        message: json["message"],
        data: json["data"] == null
            ? []
            : List<NewClubOpeningList>.from(
                json["data"]!.map((x) => NewClubOpeningList.from<PERSON><PERSON>(x))),
        count: json["count"],
        statusCode: json["statusCode"],
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "count": count,
        "statusCode": statusCode,
      };
}

class NewClubOpeningList {
  final int? clubId;
  final String? clubName;
  final String? clubType;
  final String? clubCount;
  final List<int?>? matchedMeetingIds;
  final int? isCurrentlyReadingMatch;
  final int? latestMeetingId;
  final int? latestMeetingDate;
  final String? latestBookName;
  final String? latestBookAuthor;
  final String? latestPartsOfBookCovered;
  final int? latestMeetingEndTime;
  final int? latestMeetingStartTime;
  final String? latestMeetingDuration;
  final int? clubCreatedDate;
  final int? totalVacancies;
  final List<MatchedMeeting>? matchedMeetings;
  final String? memberRequestPrompt;

  NewClubOpeningList({
    this.clubId,
    this.clubName,
    this.clubType,
    this.clubCount,
    this.matchedMeetingIds,
    this.isCurrentlyReadingMatch,
    this.latestMeetingId,
    this.latestMeetingDate,
    this.latestBookName,
    this.latestBookAuthor,
    this.latestPartsOfBookCovered,
    this.latestMeetingEndTime,
    this.latestMeetingStartTime,
    this.latestMeetingDuration,
    this.clubCreatedDate,
    this.totalVacancies,
    this.matchedMeetings,
    this.memberRequestPrompt,
  });

  factory NewClubOpeningList.fromJson(Map<String, dynamic> json) =>
      NewClubOpeningList(
        clubId: json["clubId"],
        clubName: json["clubName"],
        clubType: json["clubType"],
        clubCount: json["clubCount"],
        matchedMeetingIds: json["matchedMeetingIds"] == null
            ? []
            : List<int?>.from(json["matchedMeetingIds"]!.map((x) => x)),
        isCurrentlyReadingMatch: json["isCurrentlyReadingMatch"],
        latestMeetingId: json["latestMeetingId"],
        latestMeetingDate: json["latestMeetingDate"],
        latestBookName: json["latestBookName"],
        latestBookAuthor: json["latestBookAuthor"],
        latestPartsOfBookCovered: json["latestPartsOfBookCovered"],
        latestMeetingEndTime: json["latestMeetingEndTime"],
        latestMeetingStartTime: json["latestMeetingStartTime"],
        latestMeetingDuration: json["latestMeetingDuration"],
        clubCreatedDate: json["clubCreatedDate"],
        totalVacancies: json["totalVacancies"],
        matchedMeetings: json["matchedMeetings"] == null
            ? []
            : List<MatchedMeeting>.from(json["matchedMeetings"]!
                .map((x) => MatchedMeeting.fromJson(x))),
        memberRequestPrompt: json['memberRequestPrompt'],
      );

  Map<String, dynamic> toJson() => {
        "clubId": clubId,
        "clubName": clubName,
        "clubType": clubType,
        "clubCount": clubCount,
        "matchedMeetingIds": matchedMeetingIds == null
            ? []
            : List<dynamic>.from(matchedMeetingIds!.map((x) => x)),
        "isCurrentlyReadingMatch": isCurrentlyReadingMatch,
        "latestMeetingId": latestMeetingId,
        "latestMeetingDate": latestMeetingDate,
        "latestBookName": latestBookName,
        "latestBookAuthor": latestBookAuthor,
        "latestPartsOfBookCovered": latestPartsOfBookCovered,
        "latestMeetingEndTime": latestMeetingEndTime,
        "latestMeetingStartTime": latestMeetingStartTime,
        "latestMeetingDuration": latestMeetingDuration,
        "clubCreatedDate": clubCreatedDate,
        "totalVacancies": totalVacancies,
        "matchedMeetings": matchedMeetings == null
            ? []
            : List<dynamic>.from(matchedMeetings!.map((x) => x.toJson())),
        "memberRequestPrompt": memberRequestPrompt,
      };
}

class MatchedMeeting {
  final int? meetingId;
  final int? bookClubId;
  final String? bookName;
  final String? partOfBookCovered;
  final int? meetingDate;
  final int? meetingStartTime;
  final String? meetingDuration;
  final String? discussionQuestions;
  final String? meetingStatus;
  final String? bookAuthor;
  final String? meetingAlerts;
  final int? meetingEndTime;
  final String? token;
  final int? userId;
  final String? channelName;

  MatchedMeeting({
    this.meetingId,
    this.bookClubId,
    this.bookName,
    this.partOfBookCovered,
    this.meetingDate,
    this.meetingStartTime,
    this.meetingDuration,
    this.discussionQuestions,
    this.meetingStatus,
    this.bookAuthor,
    this.meetingAlerts,
    this.meetingEndTime,
    this.token,
    this.userId,
    this.channelName,
  });

  factory MatchedMeeting.fromJson(Map<String, dynamic> json) => MatchedMeeting(
        meetingId: json["meetingId"],
        bookClubId: json["bookClubId"],
        bookName: json["bookName"],
        partOfBookCovered: json["partOfBookCovered"],
        meetingDate: json["meetingDate"],
        meetingStartTime: json["meetingStartTime"],
        meetingDuration: json["meetingDuration"],
        discussionQuestions: json["discussionQuestions"],
        meetingStatus: json["meetingStatus"],
        bookAuthor: json["bookAuthor"],
        meetingAlerts: json["meetingAlerts"],
        meetingEndTime: json["meetingEndTime"],
        token: json["token"],
        userId: json["userId"],
        channelName: json["channelName"],
      );

  Map<String, dynamic> toJson() => {
        "meetingId": meetingId,
        "bookClubId": bookClubId,
        "bookName": bookName,
        "partOfBookCovered": partOfBookCovered,
        "meetingDate": meetingDate,
        "meetingStartTime": meetingStartTime,
        "meetingDuration": meetingDuration,
        "discussionQuestions": discussionQuestions,
        "meetingStatus": meetingStatus,
        "bookAuthor": bookAuthor,
        "meetingAlerts": meetingAlerts,
        "meetingEndTime": meetingEndTime,
        "token": token,
        "userId": userId,
        "channelName": channelName,
      };
}
