import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/app/features/messaging/models/custom_chat_message.dart';
import 'package:eljunto/app/features/messaging/providers/chat_provider.dart';
import 'package:eljunto/models/club_membership_model.dart';
import 'package:eljunto/reusableWidgets/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

void showReactionsBottomSheet({
  required BuildContext context,
  required Map<String, dynamic> reactionsMap,
  required CustomChatMessage message,
  required ChatProvider controller,
}) {
  final List<Map<String, String>> reactionList = [];
  reactionsMap.forEach((userId, emoji) {
    final user = controller.members.firstWhere(
      (m) => m.userId.toString() == userId,
      orElse: () => ClubMembershipModel(),
    );
    reactionList.add({
      'userId': userId,
      'userName': user.userId == controller.loggedInUserId
          ? 'You'
          : (user.userHandle ?? 'Unknown'),
      'emoji': emoji.toString(),
      'profilePicture': user.userProfilePicture != null
          ? '${ApiConstants.imageBaseUrl}${user.userProfilePicture}'
          : '',
    });
  });

  showModalBottomSheet(
    context: context,
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
    backgroundColor: AppConstants.textGreenColor,
    useSafeArea: true,
    builder: (_) => Padding(
      padding: const EdgeInsets.only(bottom: 10),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: reactionList.map((reaction) {
          final id = reaction['userId'];
          final name = reaction['userName'];
          final emoji = reaction['emoji'];
          final profilePicture = reaction['profilePicture'];
          return ListTile(
            leading: CircleAvatar(
              backgroundColor: Colors.white,
              child: (profilePicture?.contains('https') ?? false)
                  ? CustomCachedNetworkImage(
                      width: 40,
                      height: 40,
                      imageUrl: profilePicture,
                      errorImage: AppConstants.profileLogoImagePath,
                    )
                  : Image.asset(AppConstants.profileLogoImagePath),
            ),
            trailing: Text(emoji ?? '', style: lbBold.copyWith(fontSize: 25)),
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(name ?? '', style: lbBold.copyWith(fontSize: 14)),
                if (id == controller.loggedInUserId.toString())
                  Text(
                    'Tap to remove',
                    style: lbRegular.copyWith(
                      fontSize: 10,
                      fontWeight: FontWeight.w300,
                    ),
                  ),
              ],
            ),
            onTap: () {
              context.pop();
              if (id == controller.loggedInUserId.toString()) {
                controller.removeEmojiReaction(
                  message.customMessageProperties!['message_id'],
                );
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    behavior: SnackBarBehavior.floating,
                    backgroundColor: AppConstants.backgroundColor,
                    elevation: 5,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.all(Radius.circular(10)),
                      side: BorderSide(
                        color: AppConstants.primaryColor,
                        width: 1.5,
                      ),
                    ),
                    duration: const Duration(seconds: 2),
                    content: Text(
                      'You cannot remove other users\' reactions',
                      style: lbBold.copyWith(
                        fontSize: 14,
                        fontWeight: FontWeight.w800,
                      ),
                    ),
                  ),
                );
              }
            },
          );
        }).toList(),
      ),
    ),
  );
}
