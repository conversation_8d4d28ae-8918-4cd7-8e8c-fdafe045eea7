import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:flutter/material.dart';

class ChatInput<PERSON>ield extends StatelessWidget {
  final TextEditingController textController;
  final bool isInternetConnected;
  final VoidCallback onSend;

  const ChatInputField({
    super.key,
    required this.textController,
    required this.isInternetConnected,
    required this.onSend,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      margin: const EdgeInsets.only(top: 10),
      decoration: const BoxDecoration(
        color: Colors.transparent,
        border: Border(
          top: BorderSide(
            color: AppConstants.primaryColor,
            width: 1.5,
          ),
        ),
      ),
      child: Safe<PERSON>rea(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: Text<PERSON>ield(
                controller: textController,
                textCapitalization: TextCapitalization.sentences,
                style: lbRegular.copyWith(
                  fontSize: 14,
                  color: AppConstants.primaryColor,
                ),
                cursorColor: AppConstants.textGreenColor,
                maxLines: 5,
                minLines: 1,
                decoration: InputDecoration(
                  labelText: "Message",
                  labelStyle: lbRegular.copyWith(
                    fontSize: 14,
                    color: AppConstants.primaryColor,
                  ),
                  isDense: true,
                  contentPadding: const EdgeInsets.all(10),
                  focusedBorder: OutlineInputBorder(
                    borderSide: const BorderSide(
                      width: 1.5,
                      color: AppConstants.primaryColor,
                    ),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  border: OutlineInputBorder(
                    borderSide: const BorderSide(
                      width: 1.5,
                      color: AppConstants.primaryColor,
                    ),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderSide: const BorderSide(
                      width: 1.5,
                      color: AppConstants.primaryColor,
                    ),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  filled: false,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 10.0),
              child: IconButton(
                onPressed: isInternetConnected ? onSend : null,
                icon: Icon(
                  Icons.send,
                  color: isInternetConnected
                      ? AppConstants.primaryColor
                      : Colors.grey.withValues(alpha: .5),
                  size: 30,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
