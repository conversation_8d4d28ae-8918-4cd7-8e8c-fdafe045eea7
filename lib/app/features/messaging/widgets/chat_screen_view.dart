import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/app/features/messaging/models/custom_chat_message.dart';
import 'package:eljunto/app/features/messaging/providers/chat_provider.dart';
import 'package:eljunto/app/features/messaging/widgets/chat_input_field.dart';
import 'package:eljunto/app/features/messaging/widgets/message_row.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class CustomChatView extends StatefulWidget {
  final ChatProvider controller;
  final TextEditingController textController;
  final bool isInternetConnected;
  final VoidCallback onSend;

  const CustomChatView({
    super.key,
    required this.controller,
    required this.textController,
    required this.isInternetConnected,
    required this.onSend,
  });

  @override
  State<CustomChatView> createState() => CustomChatViewState();
}

class CustomChatViewState extends State<CustomChatView> {
  final _scrollController = ScrollController();
  bool _showScrollToBottomButton = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_handleScroll);
  }

  void _handleScroll() {
    // Show the "scroll to bottom" button if user scrolls up
    if (_scrollController.offset > 300 && !_showScrollToBottomButton) {
      if (mounted) setState(() => _showScrollToBottomButton = true);
    } else if (_scrollController.offset <= 300 && _showScrollToBottomButton) {
      if (mounted) setState(() => _showScrollToBottomButton = false);
    }

    // Trigger "load more" when the user reaches the top of the list
    if (_scrollController.position.atEdge &&
        _scrollController.position.pixels != 0) {
      if (!widget.controller.isLoadingMore) {
        widget.controller.loadMoreMessages();
      }
    }
  }

  // Helper to check if a message is on a new day
  bool _isNewDay(DateTime date1, DateTime date2) {
    return date1.year != date2.year ||
        date1.month != date2.month ||
        date1.day != date2.day;
  }

  @override
  void dispose() {
    _scrollController.removeListener(_handleScroll);
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: Stack(
            children: [
              _buildMessageList(),
              if (_showScrollToBottomButton) _buildScrollToBottom(),
            ],
          ),
        ),
        ChatInputField(
          textController: widget.textController,
          isInternetConnected: widget.isInternetConnected,
          onSend: widget.onSend,
        ),
      ],
    );
  }

  Widget _buildMessageList() {
    return ListView.builder(
      controller: _scrollController,
      reverse: true,
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      itemCount: widget.controller.messages.length +
          (widget.controller.isLoadingMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (widget.controller.isLoadingMore &&
            index == widget.controller.messages.length) {
          return const Padding(
            padding: EdgeInsets.symmetric(vertical: 20),
            child: Center(
              child: CircularProgressIndicator(
                color: AppConstants.primaryColor,
                strokeWidth: 5,
              ),
            ),
          );
        }

        final message = widget.controller.messages[index];
        final previousMessageInTime =
            (index + 1 < widget.controller.messages.length)
                ? widget.controller.messages[index + 1]
                : null;
        final nextMessageInTime =
            (index > 0) ? widget.controller.messages[index - 1] : null;

        bool isFirstMessage = previousMessageInTime == null;
        Widget content = Column(
          children: [
            if (isFirstMessage ||
                (_isNewDay(message.createdAt, previousMessageInTime.createdAt)))
              _buildDateSeparator(message.createdAt),
            _buildMessageRow(message, previousMessageInTime, nextMessageInTime),
          ],
        );

        return content;
      },
    );
  }

  Widget _buildMessageRow(
    CustomChatMessage message,
    CustomChatMessage? previousMessage,
    CustomChatMessage? nextMessage,
  ) {
    final isSystemMessage =
        message.customMessageProperties?['message_type'] == 'system';
    if (isSystemMessage) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 20.0),
          child: Text(
            message.text,
            textAlign: TextAlign.center,
            style: lbRegular.copyWith(
              fontSize: 12,
              color: AppConstants.primaryColor,
            ),
          ),
        ),
      );
    }

    final isOutgoing = message.user.id == widget.controller.currentUser!.id;

    // Logic for showing username based on the previous message in time
    bool showUsername = !isOutgoing &&
        (previousMessage == null ||
            previousMessage.user.id != message.user.id ||
            previousMessage.customMessageProperties?['message_type'] ==
                'system' ||
            _isNewDay(message.createdAt, previousMessage.createdAt));

    // Logic for showing avatar based on the next message in time
    bool showAvatar = !isOutgoing &&
        (nextMessage == null ||
            nextMessage.user.id != message.user.id ||
            nextMessage.customMessageProperties?['message_type'] == 'system' ||
            _isNewDay(nextMessage.createdAt, message.createdAt));

    return MessageRow(
      message: message,
      showUsername: showUsername,
      showAvatar: showAvatar,
      isOutgoing: isOutgoing,
      controller: widget.controller,
    );
  }

  Widget _buildDateSeparator(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final messageDate = DateTime(date.year, date.month, date.day);
    final difference = today.difference(messageDate).inDays;
    String formattedDate;
    if (difference == 0) {
      formattedDate = 'Today';
    } else if (difference == 1) {
      formattedDate = 'Yesterday';
    } else {
      formattedDate = DateFormat('EEE, MMM dd, yyyy').format(date);
    }
    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 10),
        child: Text(
          formattedDate,
          style: lbRegular.copyWith(
            fontSize: 10,
            color: AppConstants.primaryColor,
          ),
        ),
      ),
    );
  }

  Widget _buildScrollToBottom() {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Padding(
        padding: const EdgeInsets.only(bottom: 16.0),
        child: GestureDetector(
          onTap: () => _scrollController.animateTo(
            0.0,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeOut,
          ),
          child: Container(
            padding: const EdgeInsets.all(5),
            decoration: BoxDecoration(
              color: AppConstants.textGreenColor,
              borderRadius: BorderRadius.circular(50),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.5),
                  spreadRadius: 2,
                  blurRadius: 5,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: const Icon(
              Icons.arrow_downward_outlined,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }
}
