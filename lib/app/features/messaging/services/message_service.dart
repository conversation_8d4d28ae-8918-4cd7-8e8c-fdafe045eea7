import 'package:dio/dio.dart' as dio;
import 'package:eljunto/app/core/network/http/http_service.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';

import '../../../core/constants.dart';

class MessageService {
  final String baseURL = ApiConstants.flavorBaseUrl;
  final _apiService = locator<HttpApiService>();
  Future<dio.Response> getBookClubsForChat(String? bookClubType, int? userId,
      int? bookClubId, int offSet, int limit) async {
    return await _apiService.get(
        '$baseURL/book_club/list?bookClubType=$bookClubType&userId=$userId&bookClubId=$bookClubId&limit=$limit&offset=$offSet');
  }
}
