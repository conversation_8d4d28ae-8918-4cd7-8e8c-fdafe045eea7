import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/providers/connectivity_provider.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/app/core/widgets/custom_scaffold.dart';
import 'package:eljunto/app/features/messaging/models/custom_chat_message.dart';
import 'package:eljunto/app/features/messaging/providers/chat_provider.dart';
import 'package:eljunto/app/features/messaging/providers/message_provider.dart';
import 'package:eljunto/app/features/messaging/widgets/chat_screen_view.dart';
import 'package:eljunto/app/features/messaging/widgets/message_app_bar_title.dart';
import 'package:eljunto/controller/book_club_controller.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
import 'package:provider/provider.dart';

import '../../core/widgets/unified_app_bar.dart';

class ChatScreen extends StatelessWidget {
  final int bookClubId;
  final String? bookClubName;

  const ChatScreen({
    super.key,
    required this.bookClubId,
    this.bookClubName,
  });

  @override
  Widget build(BuildContext context) {
    try {
      return ChangeNotifierProvider(
        create: (context) => ChatProvider(
          bookClubId: bookClubId,
          bookClubController: context.read<BookClubController>(),
          messageController: context.read<MessageProvider>(),
          initialBookClubName: bookClubName,
        )..initialize(),
        child: const _ChatScreenView(),
      );
    } catch (e) {
      return Scaffold(
        body: Center(
          child: Text('Failed to initialize chat: ${e.toString()}'),
        ),
      );
    }
  }
}

class _ChatScreenView extends StatefulWidget {
  const _ChatScreenView();

  @override
  State<_ChatScreenView> createState() => _ChatScreenViewState();
}

class _ChatScreenViewState extends State<_ChatScreenView> {
  final _textController = TextEditingController();
  final _connectivityProvider = locator<ConnectivityProvider>();
  bool _isInternetConnected = true;

  @override
  void initState() {
    super.initState();
    _connectivityProvider.statusStream.listen((status) {
      if (mounted) {
        setState(
          () => _isInternetConnected = status == InternetStatus.connected,
        );
      }
    });
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  void _onSend(ChatProvider controller) {
    if (_textController.text.trim().isEmpty) return;
    if (controller.currentUser == null) return;

    final message = CustomChatMessage(
      user: controller.currentUser!,
      createdAt: DateTime.now(),
      text: _textController.text.trim(),
    );
    controller.sendMessage(message);
    _textController.clear();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ChatProvider>(
      builder: (context, controller, child) {
        return CustomScaffold(
          appBar: _buildAppBar(context, controller),
          body: _buildBody(context, controller),
        );
      },
    );
  }

  PreferredSizeWidget _buildAppBar(
    BuildContext context,
    ChatProvider controller,
  ) {
    // Show loading/offline state using MessageAppBarTitle
    if (controller.isLoading || !_isInternetConnected) {
      return MessageAppBarTitle(
        isInternetFlag: _isInternetConnected,
      );
    }

    // Show normal chat state using UnifiedAppBar
    final clubName = controller.initialBookClubName?.isNotEmpty ?? false
        ? controller.initialBookClubName
        : (controller.members.isNotEmpty
            ? controller.members.first.bookClubName ?? ''
            : '');

    return UnifiedAppBar.message(
      bookName: clubName ?? 'Chat',
      showBackButton: true,
      clubMembers: controller.members,
      onTitleTap: () => context.goNamed(
        "message-user-club-details",
        queryParameters: {
          'bookClubId': controller.bookClubId.toString(),
          'userId': controller.loggedInUserId.toString(),
        },
      ),
    );
  }

  Widget _buildBody(BuildContext context, ChatProvider controller) {
    if (controller.isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: AppConstants.primaryColor),
      );
    }

    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: CustomChatView(
        controller: controller,
        textController: _textController,
        isInternetConnected: _isInternetConnected,
        onSend: () => _onSend(controller),
      ),
    );
  }
}
