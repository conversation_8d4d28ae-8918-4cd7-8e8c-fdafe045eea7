import 'package:flutter/material.dart';

import 'utils/app_config.dart';

class AppConstants {
  static const String appName = "El Junto";
  static const String appVersion = "1.0.0";
  static const String appNameWithVersion = "El Junto v1.1.1";
  static const int otpExpiryTime = 0; // 10 minutes
  static const Color primaryColor = Color(0xFF253943);
  static Color ratingColor =
      const Color(0xFF253943).withValues(alpha: 60).withAlpha(60);
  static const Color backgroundColor = Color(0xFFFFF5D6);
  static const Color popUpBorderColor = Color.fromRGBO(37, 57, 67, 0.8);
  static const Color textGreenColor = Color(0xFF7C9C9A);
  static const Color blueColor = Color(0xFF3100F6);
  static const Color redColor = Color(0xFFF60303);
  static const Color hintTextColor = Color(0xFFA1A1A1);
  static const Color skeletonBackgroundColor = Color(0xFFE0E0E0);
  static const Color skeletonforgroundColor = Color(0xFFC2C2C2);
  static const Color videoConferenceAppBarColor = Color(0xFF2D2D2D);
  static const Color isActiveRequestColor = Color(0xFFBEC4C7);
  static const String assetImagePath = 'assets/images/';
  static const String bgImagePath = 'assets/images/PaperBackground.png';
  static const String closePopupImagePath = 'assets/icons/Cancel_Popups.png';
  static const String profileLogoImagePath = 'assets/icons/Profile_2.png';
  static const String eJuntoBookClubLogoPng =
      'assets/images/ElJuntoBookClubLogoPng.png';
  static const String leaderStar =
      "assets/icons/Leader_Transparent_Background.png";
  static const String ratingsStar = 'assets/icons/Rating_Star.png';
  static const String locationImagePath = "assets/icons/Location.png";
  static const String openToInvitationImagePath =
      "assets/icons/Open_to_Invitation.png";
  static const String notOpenToInvitationImagePath =
      "assets/icons/NotOpenToInvitation.png";
  static const String clubOpeningLogoImagePath =
      "assets/icons/Club_Opening_1.png";
  static const String clubLogoImagePath = "assets/icons/Clubs_2.png";
  static const String questionLogoImagePath = "assets/images/Question.png";
  static final Uri privacyPolicyUrl =
      Uri.parse('https://eljunto.com/privacypolicy.html');
  static final Uri termsAndConditionUrl =
      Uri.parse('https://eljunto.com/termsofservice.html');
  static const String notificationImagePath = "assets/icons/Notification.png";
  static const String currentlyReadingIcon =
      "assets/icons/Currently_Reading.png";
  static const String topShelfBookIcon = "assets/icons/Top_Shelf_book.png";
  static const String toBeReadIcon = "assets/images/To-Be-Read_.png";
  static const String bookReadIcon = "assets/images/all_book_read.png";

  static const String clubOpeningZero = "assets/icons/club_opening_0.png";
  static const String elJuntoLogo = "assets/images/ElJunto_logo.png";
  static const String requestDoneImg = "assets/icons/Done.png";
  static const int paginationLimit = 10;

  /// New paper background
  static const String newPaperBackground = "assets/images/new-paper-bg.png";
}

class ApiConstants {
  // DEVELOPMENT SERVER
  static const String baseUrlDev =
      'https://devapi.eljunto.com/eljuntobookclub/api';

  //PRODUCTION SERVER
  static const String baseUrlProd =
      'https://api.eljunto.com/eljuntobookclub/api';

  static String flavorBaseUrl =
      AppConfig.shared.flavor == Flavor.dev ? baseUrlDev : baseUrlProd;

  static const String imageBaseUrl = 'https://el-junto.s3.amazonaws.com/';
}
