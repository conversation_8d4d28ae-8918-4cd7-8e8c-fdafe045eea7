import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../controller/book_club_controller.dart';
import '../../../../controller/login_controller.dart';
import '../../features/messaging/providers/message_provider.dart';
import '../helpers/database_helper.dart';
import 'notification_service.dart';

class SessionManager extends ChangeNotifier {
  static final SessionManager _instance = SessionManager._internal();

  factory SessionManager() => _instance;

  SessionManager._internal();

  SharedPreferences? _prefs;

  // In-memory cache for user data
  int? _userId;
  String? _token;
  String? _userEmail;
  String? _userName;
  String? _userHandle;
  String? _userLocation;
  String? _userBio;

  // Getters for synchronous access
  int? get userId => _userId;

  String? get token => _token;

  String? get userEmail => _userEmail;

  String? get userName => _userName;

  String? get userHandle => _userHandle;

  String? get userLocation => _userLocation;

  String? get userBio => _userBio;

  bool get isLoggedIn => _token != null;

  // --- Initialization ---
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    _loadSessionData();
  }

  void _loadSessionData() {
    _userId = _prefs?.getInt('userId');
    _token = _prefs?.getString('token');
    _userEmail = _prefs?.getString('userEmailId');
    _userName = _prefs?.getString('userName');
    _userHandle = _prefs?.getString('userHandle');
    _userLocation = _prefs?.getString('userLocation');
    _userBio = _prefs?.getString('userBio');
    log("Session data loaded for user ID: $_userId");
    notifyListeners();
  }

  // --- Session Management ---

  Future<void> createSession({
    required int? userId,
    required String? token,
    required String? userEmail,
    required String? userName,
    required String? userHandle,
    String? userLocation,
    String? userBio,
  }) async {
    _prefs ??= await SharedPreferences.getInstance();

    await safeSetInt('userId', userId);
    await safeSetString('token', token);
    await safeSetString('userEmailId', userEmail);
    await safeSetString('userName', userName);
    await safeSetString('userHandle', userHandle);
    await safeSetString('userLocation', userLocation);
    await safeSetString('userBio', userBio);

    _loadSessionData(); // Reload data into cache and notify listeners
  }

  // --- Safe Public Setters ---

  Future<void> safeSetString(String key, String? value) async {
    if (value == null || value.isEmpty) return;
    _prefs ??= await SharedPreferences.getInstance();
    await _prefs?.setString(key, value);
    _loadSessionData();
  }

  Future<void> safeSetInt(String key, int? value) async {
    if (value == null) return;
    _prefs ??= await SharedPreferences.getInstance();
    await _prefs?.setInt(key, value);
    _loadSessionData();
  }

  Future<void> safeSetBool(String key, bool? value) async {
    if (value == null) return;
    _prefs ??= await SharedPreferences.getInstance();
    await _prefs?.setBool(key, value);
    _loadSessionData();
  }

  // --- Safe Public Getters ---

  Future<String?> safeGetString(String key) async {
    _prefs ??= await SharedPreferences.getInstance();
    return _prefs?.getString(key);
  }

  Future<int?> safeGetInt(String key) async {
    _prefs ??= await SharedPreferences.getInstance();
    return _prefs?.getInt(key);
  }

  Future<bool?> safeGetBool(String key) async {
    _prefs ??= await SharedPreferences.getInstance();
    return _prefs?.getBool(key);
  }

  Future<void> remove(String key) async {
    _prefs ??= await SharedPreferences.getInstance();
    await _prefs?.remove(key);
  }

  Future<void> clear() async {
    _prefs ??= await SharedPreferences.getInstance();
    await _prefs?.clear();
  }

  Future<void> userLogout(
      BuildContext context, Map<String, dynamic> payload) async {
    final email = userEmail;
    if (email == null) {
      await _clearSession(context);
      return;
    }

    log("Logging out user with email: $email");
    bool logoutStatus = false;
    if (context.mounted) {
      logoutStatus = await _performApiLogout(context, email, payload);
    }

    log("Logout API Status: $logoutStatus");
    await _clearSession(context);
  }

  Future<bool> _performApiLogout(
    BuildContext context,
    String userMailId,
    Map<String, dynamic> payload,
  ) async {
    try {
      final bookClubController =
          Provider.of<BookClubController>(context, listen: false);
      final loginController =
          Provider.of<LoginController>(context, listen: false);

      await bookClubController.agoraLogs(payload);
      log("Logout from Agora successful.");

      final success = await loginController.logOutFunction(userMailId);
      log("Backend logout status: $success");
      return success;
    } catch (e) {
      log("Error during API logout: $e");
      return false;
    }
  }

  Future<void> _clearSession(BuildContext context) async {
    _prefs ??= await SharedPreferences.getInstance();

    if (context.mounted) {
      Provider.of<MessageProvider>(context, listen: false).logoutUser();
    }
    NotificationServices().cancelAllOperations();
    await DatabaseHelper.instance.clearEntireDatabase();
    await _prefs?.clear();

    // Clear in-memory cache
    _userId = null;
    _token = null;
    _userEmail = null;
    _userName = null;
    _userHandle = null;
    _userLocation = null;
    _userBio = null;

    log("Session cleared. Navigating to login.");
    notifyListeners();

    if (context.mounted) {
      context.goNamed('login');
    }
  }
}
