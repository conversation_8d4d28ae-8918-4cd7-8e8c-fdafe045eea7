import 'dart:developer';

import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/providers/connectivity_provider.dart';
import 'package:eljunto/app/core/routes/route_persistence.dart';
import 'package:eljunto/app/core/services/session_manager.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/app/features/messaging/providers/message_provider.dart';
import 'package:eljunto/app/features/messaging/services/firebase_user_club_service.dart';
import 'package:eljunto/controller/subscription_controller.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

/// Handles all redirection logic for the GoRouter instance.
class AppRedirect {
  AppRedirect._();

  /// A flag to track if the app has been initialized.
  static bool appInitialized = false;
  static final sessionManager = locator<SessionManager>();

  /// The main redirection method for the router.
  static Future<String?> redirect(
      BuildContext context, GoRouterState state) async {
    log("--- Redirect START for ${state.uri} (App Initialized: $appInitialized) ---");

    // Handle uninitialized app state
    if (!appInitialized) {
      return _handleUninitializedApp(state.uri.toString());
    }

    // Handle connectivity issues
    final connectivityProvider = locator<ConnectivityProvider>();
    if (connectivityProvider.status == InternetStatus.disconnected) {
      log("Redirect: No internet. Navigating to No Network screen.");
      return '/full-no-network';
    }

    // Handle external URLs
    final externalUrlRedirect = await _handleExternalUrl(state.uri.toString());
    if (externalUrlRedirect != null) {
      return externalUrlRedirect;
    }

    // Handle pending routes from app start
    if (state.uri.toString() == '/') {
      final pendingRoute = await RoutePersistence.getPendingRouteAndClear();
      if (pendingRoute != null) {
        return pendingRoute;
      }
    }

    // Perform deep link validation
    final deepLinkRedirect = await _validateDeepLink(context, state);
    if (deepLinkRedirect != null) {
      return deepLinkRedirect;
    }

    // Perform authentication and subscription checks
    final authRedirect = await _handleAuthAndSubscription(context, state);
    if (authRedirect != null) {
      return authRedirect;
    }

    log("No redirect needed for ${state.uri}. Allowing navigation.");
    return null; // No redirection needed
  }

  /// Stores the requested path and redirects to the splash screen if the app
  /// hasn't initialized yet.
  static String? _handleUninitializedApp(String requestedPath) {
    if (requestedPath == '/') {
      return null; // Already at the splash screen
    }
    RoutePersistence.storePendingRoute(requestedPath);
    return '/'; // Redirect to splash
  }

  /// Checks for and launches external URLs, then redirects to the home screen.
  static Future<String?> _handleExternalUrl(String path) async {
    final isExternal = [
      AppConstants.privacyPolicyUrl.toString(),
      AppConstants.termsAndConditionUrl.toString(),
      'https://eljunto.com/'
    ].contains(path);

    if (isExternal) {
      final uri = Uri.parse(path);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      }
      return '/Home'; // Redirect home after attempting to launch URL
    }
    return null;
  }

  /// Validates deep links for correct user ID and club membership.
  static Future<String?> _validateDeepLink(
      BuildContext context, GoRouterState state) async {
    final messageController =
        Provider.of<MessageProvider>(context, listen: false);
    final loggedInUserId = sessionManager.userId;
    final queryParams = state.uri.queryParameters;

    // Validate userId in deep link
    if (queryParams.containsKey('userId') &&
        !state.uri.toString().contains('chat-screen')) {
      final deepLinkUserId = int.tryParse(queryParams['userId'] ?? '');
      if (deepLinkUserId == null ||
          (loggedInUserId != null && deepLinkUserId != loggedInUserId)) {
        log("Deep link 'userId' is invalid or doesn't match. Redirecting home.");
        if (context.mounted) {
          await messageController.isInvalidDeeplink(true);
        }
        return '/Home';
      }
    }

    // Validate club membership for club-related deep links
    if (queryParams.containsKey('bookClubId') && loggedInUserId != null) {
      final clubService = FirebaseUserClubService();
      final userClubIds = await clubService.getUserClubIds();
      final targetClubId = int.tryParse(queryParams['bookClubId']!);

      if (targetClubId != null && !userClubIds.contains(targetClubId)) {
        log("Access to club $targetClubId denied via deep link. User not a member.");
        if (context.mounted) {
          await messageController.isInvalidDeeplink(true);
        }
        return '/Home';
      }
    }

    return null;
  }

  /// Handles authentication and subscription status, redirecting if necessary.
  static Future<String?> _handleAuthAndSubscription(
      BuildContext context, GoRouterState state) async {
    final subscriptionController =
        Provider.of<SubscriptionController>(context, listen: false);

    final isLoggedIn = sessionManager.isLoggedIn;
    final isPublicRoute = _isPublicRoute(state);

    if (!isLoggedIn && !isPublicRoute) {
      RoutePersistence.storePendingRoute(state.uri.toString());
      return '/login';
    }

    if (isLoggedIn) {
      final isSubscriptionActive =
          await _checkSubscriptionStatus(context, subscriptionController);
      if (!isSubscriptionActive &&
          !isPublicRoute &&
          state.matchedLocation != '/subscription') {
        RoutePersistence.storePendingRoute(state.uri.toString());
        return '/subscription';
      }
    }

    return null;
  }

  /// Checks if the current user's subscription is active.
  static Future<bool> _checkSubscriptionStatus(BuildContext context,
      SubscriptionController subscriptionController) async {
    if (!context.mounted) return false;
    try {
      if (subscriptionController.verifySubscription) {
        await subscriptionController.isActiveSubscription();
      }
      final status =
          subscriptionController.verifySubscriptionModel?.data?.usubStatus;
      return status == 'ACTIVE' || status == 'CANCELLED';
    } catch (e) {
      return false;
    }
  }

  /// Determines if the current route is a public-access route.
  static bool _isPublicRoute(GoRouterState state) {
    const publicRoutes = {
      '/',
      '/login',
      '/sign-up',
      '/otp',
      '/set-password',
      '/set-name-handle',
      '/profilesetup',
      '/privacy-policy',
      '/terms-of-service',
      '/trial-delete-account',
    };
    return publicRoutes.contains(state.matchedLocation);
  }
}
