import 'package:eljunto/app/core/widgets/transition_widget.dart';
import 'package:eljunto/app/features/messaging/chat_screen.dart';
import 'package:eljunto/app/features/subscription/views/subscription_screen.dart';
import 'package:eljunto/controller/subscription_controller.dart';
import 'package:eljunto/controller/user_credential_controller.dart';
import 'package:eljunto/reusableWidgets/connection_error/connection_lost_screen.dart';
import 'package:eljunto/reusableWidgets/connection_error/server_unavailable_screen.dart';
import 'package:eljunto/views/meeting/meeting_screen.dart';
import 'package:eljunto/views/settings/delete_account.dart';
import 'package:eljunto/views/settings/privacy_policy.dart';
import 'package:eljunto/views/settings/terms_of_service.dart';
import 'package:go_router/go_router.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
import 'package:provider/provider.dart';

import '../../../features/splash/view/splash_screen.dart';

/// Defines the top-level routes for the application.
class AppRoutes {
  AppRoutes._();

  static final List<RouteBase> routes = [
    GoRoute(
      path: '/',
      name: 'SplashScreen',
      builder: (context, state) {
        return const SplashScreen();
      },
    ),
    GoRoute(
      path: "/MeetingScreen",
      name: "MeetingScreen",
      pageBuilder: (context, state) {
        final extras = state.extra as Map;
        return TransitionPageWidget.navigateTransitionPage(
          child: MeetingScreen(
            key: state.pageKey,
            bookClubId: extras['bookClubId'],
            bookName: extras['bookName'],
            token: extras['token'],
            userId: extras['userId'],
            discussionQue: extras['discussionQue'],
            channelName: extras['channelName'],
            userHandle: extras['userName'],
            profilePictureUrl: extras['profilePictureUrl'],
          ),
        );
      },
    ),
    GoRoute(
      path: '/subscription',
      name: 'subscription',
      pageBuilder: (context, state) {
        return TransitionPageWidget.navigateTransitionPage(
          child: SubscriptionScreen(
            key: state.pageKey,
          ),
        );
      },
    ),
    GoRoute(
      path: "/chat-screen",
      name: "chat-screen",
      pageBuilder: (context, state) {
        final extras = state.uri.queryParameters;
        return TransitionPageWidget.navigateTransitionPage(
          child: ChatScreen(
            bookClubName: extras['bookClubName'] ?? '',
            bookClubId: int.tryParse(extras['bookClubId'] ?? '') ?? 0,
            key: state.pageKey,
          ),
        );
      },
    ),
    GoRoute(
      path: '/trial-delete-account',
      name: 'trial-delete-account',
      pageBuilder: (context, state) {
        return TransitionPageWidget.navigateTransitionPage(
          child: DeleteAccountPage(
            key: state.pageKey,
          ),
        );
      },
    ),
    GoRoute(
      path: '/privacy-policy',
      name: 'privacy-policy',
      pageBuilder: (context, state) {
        return TransitionPageWidget.navigateTransitionPage(
          child: PrivacyPolicyPage(
            key: state.pageKey,
          ),
        );
      },
    ),
    GoRoute(
      path: '/terms-of-service',
      name: 'terms-of-service',
      pageBuilder: (context, state) {
        return TransitionPageWidget.navigateTransitionPage(
          child: TermsOfServicePage(
            key: state.pageKey,
          ),
        );
      },
    ),
    GoRoute(
      path: '/full-no-network',
      name: 'full-no-network',
      builder: (context, state) => ConnectivityLossScreen(
        showAppBar: false,
        onTryAgain: () async {
          bool isActiveSubscription = false;
          bool isLoggedIn = false;
          final connected = await InternetConnection().hasInternetAccess;
          if (context.mounted) {
            isActiveSubscription = await Provider.of<SubscriptionController>(
                    context,
                    listen: false)
                .isActiveSubscription();
          }
          if (context.mounted) {
            isLoggedIn = await Provider.of<UserCredentialController>(context,
                    listen: false)
                .isUserLoggedIn();
          }
          if (connected && context.mounted) {
            if (!isLoggedIn) {
              context.go('/login');
            } else if (isActiveSubscription) {
              context.go('/Home');
            } else {
              context.go('/subscription');
            }
          }
        },
      ),
    ),
    GoRoute(
      path: '/server-down',
      name: 'server-down',
      builder: (context, state) => ServerUnavailableScreen(
        onTryAgain: () async {
          bool isActiveSubscription = false;
          bool isLoggedIn = false;
          final connected = await InternetConnection().hasInternetAccess;
          if (context.mounted) {
            isActiveSubscription = await Provider.of<SubscriptionController>(
                    context,
                    listen: false)
                .isActiveSubscription();
          }
          if (context.mounted) {
            isLoggedIn = await Provider.of<UserCredentialController>(context,
                    listen: false)
                .isUserLoggedIn();
          }
          if (connected && context.mounted) {
            if (!isLoggedIn) {
              context.go('/login');
            } else if (isActiveSubscription) {
              context.go('/Home');
            } else {
              context.go('/subscription');
            }
          }
        },
      ),
    ),
  ];
}
