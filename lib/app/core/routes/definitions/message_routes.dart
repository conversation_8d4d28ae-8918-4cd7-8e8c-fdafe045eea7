import 'package:eljunto/app/features/messaging/messages_screen.dart';
import 'package:eljunto/views/clubs/user_club_details/user_club_details_screen.dart';
import 'package:eljunto/views/home_screen/club_member_profile_screen.dart';
import 'package:go_router/go_router.dart';

import '../app_navigation_keys.dart';

/// Defines the routes for the 'Message' section of the application.
class MessageRoutes {
  MessageRoutes._();

  static final StatefulShellBranch branch = StatefulShellBranch(
    navigatorKey: AppNavigationKeys.messageNavigatorKey,
    routes: [
      GoRoute(
        path: '/Message',
        name: 'Message',
        builder: (context, state) => MessagesScreen(key: state.pageKey),
        routes: [
          GoRoute(
            path: "message-user-club-details",
            name: "message-user-club-details",
            pageBuilder: (context, state) {
              final queryParameters = state.uri.queryParameters;
              final bookClubId = queryParameters['bookClubId'];
              final userId = queryParameters['userId'];
              return NoTransitionPage(
                child: UserClubDetailsScreen(
                  key: state.pageKey,
                  bookClubId: int.parse(bookClubId ?? '0'),
                  fromChat: true,
                  userId: userId ?? '0',
                ),
              );
            },
          ),
          GoRoute(
            path: 'message-club-member-profile',
            name: 'message-club-member-profile',
            pageBuilder: (context, state) {
              final extra = state.extra as Map;
              return NoTransitionPage(
                child: ClubMemberProfile(
                  key: state.pageKey,
                  userId: extra['userId'],
                  fromChat: true,
                  bookClubId: extra['bookClubId'],
                ),
              );
            },
          ),
        ],
      ),
    ],
  );
}
